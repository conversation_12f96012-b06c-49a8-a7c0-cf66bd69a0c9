/* Booking Calendar Styles */

.calendarContainer {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.calendarHeader {
  background: #f8fafc;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e2e8f0;
}

.monthNavigation {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.navButton {
  background: #3b82f6;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.navButton:hover {
  background: #1d4ed8;
  transform: translateY(-1px);
}

.monthTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  min-width: 200px;
  text-align: center;
}

.todayButton {
  background: #10b981;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.todayButton:hover {
  background: #059669;
  transform: translateY(-1px);
}

.calendar {
  padding: 1rem;
}

.dayHeaders {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  margin-bottom: 1rem;
}

.dayHeader {
  padding: 0.75rem;
  text-align: center;
  font-weight: 600;
  color: #64748b;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.calendarGrid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.calendarDay {
  background: white;
  min-height: 120px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
}

.calendarDay:hover {
  background: #f8fafc;
}

.calendarDay.today {
  background: #eff6ff;
  border: 2px solid #3b82f6;
}

.calendarDay.otherMonth {
  background: #f8fafc;
  color: #94a3b8;
}

.calendarDay.otherMonth .dayNumber {
  color: #cbd5e1;
}

.dayNumber {
  font-weight: 600;
  font-size: 0.9rem;
  color: #1e293b;
  margin-bottom: 0.5rem;
  text-align: center;
}

.bookingsContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
  overflow: hidden;
}

.bookingItem {
  background: #f1f5f9;
  border-left: 3px solid #3b82f6;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.bookingItem:hover {
  background: #e2e8f0;
  transform: translateX(2px);
}

.bookingTime {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.7rem;
}

.bookingCustomer {
  color: #475569;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bookingService {
  color: #64748b;
  font-size: 0.65rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.moreBookings {
  background: #e2e8f0;
  color: #64748b;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  text-align: center;
  font-weight: 500;
  margin-top: 2px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .calendarDay {
    min-height: 100px;
  }
  
  .bookingItem {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
  
  .bookingTime {
    font-size: 0.65rem;
  }
  
  .bookingService {
    font-size: 0.6rem;
  }
}

@media (max-width: 768px) {
  .calendarHeader {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .monthTitle {
    font-size: 1.3rem;
    min-width: auto;
  }
  
  .calendar {
    padding: 0.5rem;
  }
  
  .calendarDay {
    min-height: 80px;
    padding: 0.25rem;
  }
  
  .dayNumber {
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
  }
  
  .bookingItem {
    font-size: 0.65rem;
    padding: 0.15rem 0.3rem;
    min-height: 16px;
  }
  
  .bookingTime {
    font-size: 0.6rem;
  }
  
  .bookingCustomer {
    font-size: 0.6rem;
  }
  
  .bookingService {
    display: none; /* Hide service name on mobile to save space */
  }
  
  .moreBookings {
    font-size: 0.65rem;
    padding: 0.15rem 0.3rem;
  }
}

@media (max-width: 480px) {
  .calendarGrid {
    gap: 0;
  }
  
  .calendarDay {
    min-height: 60px;
    padding: 0.2rem;
  }
  
  .dayNumber {
    font-size: 0.75rem;
  }
  
  .bookingItem {
    font-size: 0.6rem;
    padding: 0.1rem 0.2rem;
    min-height: 14px;
  }
  
  .bookingTime {
    font-size: 0.55rem;
  }
  
  .bookingCustomer {
    font-size: 0.55rem;
  }
}
