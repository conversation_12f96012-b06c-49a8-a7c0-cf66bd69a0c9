import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import AdminLayout from '@/components/admin/AdminLayout';
import styles from '@/styles/admin/Staff.module.css';

/**
 * Staff Management Page
 * 
 * This page provides a comprehensive interface for managing staff members,
 * including roles, permissions, and access control.
 */
export default function StaffManagement() {
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [staff, setStaff] = useState([]);
  const [filteredStaff, setFilteredStaff] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');

  // Load staff data
  useEffect(() => {
    if (user) {
      loadStaff();
    }
  }, [user]);

  // Filter and sort staff
  useEffect(() => {
    let filtered = [...staff];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(member =>
        member.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply role filter
    if (roleFilter !== 'all') {
      filtered = filtered.filter(member => member.role === roleFilter);
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(member => member.status === statusFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`);
        case 'role':
          return a.role.localeCompare(b.role);
        case 'email':
          return a.email.localeCompare(b.email);
        case 'lastLogin':
          return new Date(b.lastLogin || 0) - new Date(a.lastLogin || 0);
        default:
          return 0;
      }
    });

    setFilteredStaff(filtered);
  }, [staff, searchTerm, roleFilter, statusFilter, sortBy]);

  const loadStaff = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/staff', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStaff(data.staff || []);
      } else {
        console.error('Failed to load staff');
        // Mock data for development
        setStaff([
          {
            id: '1',
            firstName: 'Admin',
            lastName: 'User',
            email: '<EMAIL>',
            role: 'Admin',
            status: 'active',
            lastLogin: new Date().toISOString(),
            createdAt: '2024-01-01T00:00:00Z'
          }
        ]);
      }
    } catch (error) {
      console.error('Error loading staff:', error);
      setStaff([]);
    } finally {
      setLoading(false);
    }
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'DEV': return '#8b5cf6';
      case 'Admin': return '#ef4444';
      case 'Artist': return '#10b981';
      case 'Braider': return '#f59e0b';
      default: return '#6b7280';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#10b981';
      case 'inactive': return '#6b7280';
      case 'suspended': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const formatLastLogin = (lastLogin) => {
    if (!lastLogin) return 'Never';
    const date = new Date(lastLogin);
    const now = new Date();
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading staff...</p>
        </div>
      </AdminLayout>
    );
  }

  if (!user) {
    return null; // Will redirect to login via useAuth
  }

  // Check permissions
  if (!['DEV', 'Admin'].includes(user.role)) {
    return (
      <AdminLayout>
        <div className={styles.accessDenied}>
          <h2>Access Denied</h2>
          <p>You don't have permission to access staff management.</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Staff Management | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage staff members and permissions" />
      </Head>

      <div className={styles.staffContainer}>
        <header className={styles.header}>
          <h1 className={styles.title}>Staff Management</h1>
          <div className={styles.headerActions}>
            <Link href="/admin/staff/new" className={styles.newStaffBtn}>
              + Add Staff Member
            </Link>
          </div>
        </header>

        <div className={styles.controlsPanel}>
          <div className={styles.searchSection}>
            <input
              type="text"
              placeholder="Search staff by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>

          <div className={styles.filtersSection}>
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              className={styles.filterSelect}
            >
              <option value="all">All Roles</option>
              <option value="DEV">Developer</option>
              <option value="Admin">Admin</option>
              <option value="Artist">Artist</option>
              <option value="Braider">Braider</option>
            </select>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className={styles.filterSelect}
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="suspended">Suspended</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className={styles.sortSelect}
            >
              <option value="name">Sort by Name</option>
              <option value="role">Sort by Role</option>
              <option value="email">Sort by Email</option>
              <option value="lastLogin">Sort by Last Login</option>
            </select>
          </div>
        </div>

        <div className={styles.staffContent}>
          {filteredStaff.length === 0 ? (
            <div className={styles.emptyState}>
              <div className={styles.emptyIcon}>👥</div>
              <h3>No Staff Members Found</h3>
              <p>
                {staff.length === 0 
                  ? "No staff members have been added yet."
                  : "No staff members match your current filters."
                }
              </p>
              {staff.length === 0 && (
                <Link href="/admin/staff/new" className={styles.addFirstBtn}>
                  Add First Staff Member
                </Link>
              )}
            </div>
          ) : (
            <div className={styles.staffGrid}>
              {filteredStaff.map((member) => (
                <div key={member.id} className={styles.staffCard}>
                  <div className={styles.cardHeader}>
                    <div className={styles.memberInfo}>
                      <h3 className={styles.memberName}>
                        {member.firstName} {member.lastName}
                      </h3>
                      <p className={styles.memberEmail}>{member.email}</p>
                    </div>
                    <div className={styles.badges}>
                      <span 
                        className={styles.roleBadge}
                        style={{ backgroundColor: getRoleColor(member.role) }}
                      >
                        {member.role}
                      </span>
                      <span 
                        className={styles.statusBadge}
                        style={{ backgroundColor: getStatusColor(member.status) }}
                      >
                        {member.status}
                      </span>
                    </div>
                  </div>

                  <div className={styles.cardBody}>
                    <div className={styles.memberDetails}>
                      <p className={styles.lastLogin}>
                        Last login: {formatLastLogin(member.lastLogin)}
                      </p>
                      <p className={styles.joinDate}>
                        Joined: {new Date(member.createdAt).toLocaleDateString()}
                      </p>
                    </div>

                    <div className={styles.cardActions}>
                      <Link
                        href={`/admin/staff/${member.id}`}
                        className={styles.viewBtn}
                      >
                        View Details
                      </Link>
                      <Link
                        href={`/admin/staff/onboarding?staff_id=${member.id}`}
                        className={styles.onboardingBtn}
                      >
                        📋 Onboarding
                      </Link>
                      <Link
                        href={`/admin/staff/training?staff_id=${member.id}`}
                        className={styles.trainingBtn}
                      >
                        🎓 Training
                      </Link>
                      <Link
                        href={`/admin/staff/performance?staff_id=${member.id}`}
                        className={styles.performanceBtn}
                      >
                        📊 Performance
                      </Link>
                      <Link
                        href={`/admin/staff/${member.id}/edit`}
                        className={styles.editBtn}
                      >
                        Edit
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}
