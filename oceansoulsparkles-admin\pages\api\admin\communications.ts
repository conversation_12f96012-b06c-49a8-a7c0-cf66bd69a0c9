import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminAuth } from '../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Customer communications API called - ${req.method}`);

  try {
    // Authenticate admin request
    const authResult = await verifyAdminAuth(req);
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Authentication required',
        message: authResult.message,
        requestId
      });
    }

    const { user } = authResult;

    if (req.method === 'GET') {
      const { 
        customer_id, 
        booking_id, 
        type, 
        status, 
        limit = '50', 
        offset = '0' 
      } = req.query;

      let query = supabase
        .from('customer_communications')
        .select(`
          id,
          customer_id,
          booking_id,
          template_id,
          communication_type,
          recipient,
          subject,
          status,
          sent_at,
          delivered_at,
          opened_at,
          error_message,
          created_at,
          customers!inner(
            id,
            first_name,
            last_name,
            email
          ),
          bookings(
            id,
            start_time,
            services(name)
          ),
          email_templates(
            id,
            name,
            type
          )
        `)
        .order('created_at', { ascending: false })
        .range(parseInt(offset as string), parseInt(offset as string) + parseInt(limit as string) - 1);

      // Apply filters
      if (customer_id) {
        query = query.eq('customer_id', customer_id);
      }
      if (booking_id) {
        query = query.eq('booking_id', booking_id);
      }
      if (type && type !== 'all') {
        query = query.eq('communication_type', type);
      }
      if (status && status !== 'all') {
        query = query.eq('status', status);
      }

      const { data: communications, error } = await query;

      if (error) {
        console.error(`[${requestId}] Database error:`, error);
        return res.status(500).json({
          error: 'Failed to fetch communications',
          message: error.message,
          requestId
        });
      }

      // Get total count for pagination
      let countQuery = supabase
        .from('customer_communications')
        .select('*', { count: 'exact', head: true });

      if (customer_id) countQuery = countQuery.eq('customer_id', customer_id);
      if (booking_id) countQuery = countQuery.eq('booking_id', booking_id);
      if (type && type !== 'all') countQuery = countQuery.eq('communication_type', type);
      if (status && status !== 'all') countQuery = countQuery.eq('status', status);

      const { count: totalCount } = await countQuery;

      return res.status(200).json({
        communications: communications || [],
        total: totalCount || 0,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        requestId
      });
    }

    if (req.method === 'POST') {
      const {
        customer_id,
        booking_id,
        template_id,
        communication_type,
        recipient,
        subject,
        content,
        status = 'pending'
      } = req.body;

      // Validate required fields
      if (!customer_id || !communication_type || !recipient || !content) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Customer ID, communication type, recipient, and content are required',
          requestId
        });
      }

      const { data: communication, error } = await supabase
        .from('customer_communications')
        .insert([
          {
            customer_id,
            booking_id,
            template_id,
            communication_type,
            recipient,
            subject,
            content,
            status,
            created_by: user.id
          }
        ])
        .select(`
          id,
          customer_id,
          booking_id,
          template_id,
          communication_type,
          recipient,
          subject,
          status,
          sent_at,
          created_at
        `)
        .single();

      if (error) {
        console.error(`[${requestId}] Error creating communication:`, error);
        return res.status(500).json({
          error: 'Failed to create communication record',
          message: error.message,
          requestId
        });
      }

      return res.status(201).json({
        communication,
        message: 'Communication record created successfully',
        requestId
      });
    }

    return res.status(405).json({
      error: 'Method not allowed',
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      requestId
    });
  }
}
