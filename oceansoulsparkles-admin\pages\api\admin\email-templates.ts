import type { NextApiRequest, NextApiResponse } from 'next';
import { authenticateAdminRequest } from '../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Email templates API called - ${req.method}`);

  try {
    // Authenticate admin request
    const { user, error: authError } = await authenticateAdminRequest(req);
    if (authError || !user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        message: authError?.message || 'Authentication failed',
        requestId
      });
    }

    // Only Admin and DEV can manage email templates
    if (user.role !== 'Admin' && user.role !== 'DEV') {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        message: 'Only admins can manage email templates',
        requestId
      });
    }

    if (req.method === 'GET') {
      const { type, active_only } = req.query;

      let query = supabase
        .from('email_templates')
        .select(`
          id,
          name,
          type,
          subject,
          html_content,
          text_content,
          variables,
          is_active,
          is_default,
          created_at,
          updated_at
        `)
        .order('type', { ascending: true })
        .order('name', { ascending: true });

      // Filter by type if specified
      if (type && type !== 'all') {
        query = query.eq('type', type);
      }

      // Filter by active status if specified
      if (active_only === 'true') {
        query = query.eq('is_active', true);
      }

      const { data: templates, error } = await query;

      if (error) {
        console.error(`[${requestId}] Database error:`, error);
        return res.status(500).json({
          error: 'Failed to fetch email templates',
          message: error.message,
          requestId
        });
      }

      return res.status(200).json({
        templates: templates || [],
        total: templates?.length || 0,
        requestId
      });
    }

    if (req.method === 'POST') {
      const {
        name,
        type,
        subject,
        html_content,
        text_content,
        variables,
        is_active = true,
        is_default = false
      } = req.body;

      // Validate required fields
      if (!name || !type || !subject || !html_content) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Name, type, subject, and HTML content are required',
          requestId
        });
      }

      // Check if name already exists
      const { data: existingTemplate } = await supabase
        .from('email_templates')
        .select('id')
        .eq('name', name)
        .single();

      if (existingTemplate) {
        return res.status(409).json({
          error: 'Template name already exists',
          message: 'A template with this name already exists',
          requestId
        });
      }

      // If setting as default, unset other defaults for this type
      if (is_default) {
        await supabase
          .from('email_templates')
          .update({ is_default: false })
          .eq('type', type);
      }

      const { data: template, error } = await supabase
        .from('email_templates')
        .insert([
          {
            name,
            type,
            subject,
            html_content,
            text_content,
            variables: variables || [],
            is_active,
            is_default,
            created_by: user.id,
            updated_by: user.id
          }
        ])
        .select()
        .single();

      if (error) {
        console.error(`[${requestId}] Error creating template:`, error);
        return res.status(500).json({
          error: 'Failed to create email template',
          message: error.message,
          requestId
        });
      }

      return res.status(201).json({
        template,
        message: 'Email template created successfully',
        requestId
      });
    }

    return res.status(405).json({
      error: 'Method not allowed',
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      requestId
    });
  }
}
