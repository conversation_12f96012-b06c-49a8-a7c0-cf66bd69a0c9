import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminToken } from '../../../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '') || 
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const authResult = await verifyAdminToken(token);
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    const { id: serviceId } = req.query;

    if (!serviceId || typeof serviceId !== 'string') {
      return res.status(400).json({ error: 'Invalid service ID' });
    }

    // Verify service exists
    const { data: service, error: serviceError } = await supabase
      .from('services')
      .select('id, name')
      .eq('id', serviceId)
      .single();

    if (serviceError || !service) {
      return res.status(404).json({ error: 'Service not found' });
    }

    // Get service pricing tiers
    const { data: tiers, error } = await supabase
      .from('service_pricing_tiers')
      .select(`
        id,
        name,
        description,
        price,
        duration,
        is_default,
        sort_order
      `)
      .eq('service_id', serviceId)
      .order('sort_order', { ascending: true });

    if (error) {
      console.error('Service tiers query error:', error);
      return res.status(500).json({ error: 'Failed to fetch service tiers' });
    }

    return res.status(200).json({
      tiers: tiers || [],
      total: tiers?.length || 0,
      service: service
    });

  } catch (error) {
    console.error('Service tiers API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
