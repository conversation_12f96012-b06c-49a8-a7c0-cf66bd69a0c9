/* Staff Management Page Styles */

.staffContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.title {
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.newStaffBtn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.newStaffBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.controlsPanel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1.5rem 2rem;
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.searchSection {
  flex: 1;
  min-width: 300px;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
}

.searchInput:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filtersSection {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.filterSelect, .sortSelect {
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  font-size: 0.875rem;
  cursor: pointer;
}

.staffContent {
  padding: 2rem;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: #64748b;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.emptyState h3 {
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.addFirstBtn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

.addFirstBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.staffGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.staffCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.staffCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.memberInfo {
  flex: 1;
}

.memberName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.memberEmail {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0;
}

.badges {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-end;
}

.roleBadge, .statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.cardBody {
  border-top: 1px solid #e2e8f0;
  padding-top: 1rem;
}

.memberDetails {
  margin-bottom: 1rem;
}

.lastLogin, .joinDate {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0.25rem 0;
}

.cardActions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.viewBtn, .editBtn, .onboardingBtn, .trainingBtn, .performanceBtn {
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  text-decoration: none;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.3s ease;
  text-align: center;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
}

.viewBtn {
  background: #f8fafc;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.viewBtn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.editBtn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
}

.editBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.4);
}

.onboardingBtn {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fed7aa;
}

.onboardingBtn:hover {
  background: #fed7aa;
  border-color: #fdba74;
}

.trainingBtn {
  background: #dbeafe;
  color: #1d4ed8;
  border: 1px solid #93c5fd;
}

.trainingBtn:hover {
  background: #93c5fd;
  border-color: #60a5fa;
}

.performanceBtn {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.performanceBtn:hover {
  background: #a7f3d0;
  border-color: #6ee7b7;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: #64748b;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.accessDenied {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: #64748b;
}

.accessDenied h2 {
  color: #1e293b;
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .controlsPanel {
    flex-direction: column;
    align-items: stretch;
  }

  .filtersSection {
    flex-direction: column;
  }

  .staffGrid {
    grid-template-columns: 1fr;
  }

  .staffContent {
    padding: 1rem;
  }
}
