/* Email Templates Page Styles */

.templatesContainer {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.headerLeft {
  flex: 1;
}

.title {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.createBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.createBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.errorMessage {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.closeError {
  background: none;
  border: none;
  color: #dc2626;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filters {
  display: flex;
  gap: 24px;
  align-items: center;
  margin-bottom: 32px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.filterGroup {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filterGroup label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.filterSelect {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 150px;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.checkboxLabel input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.templatesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
}

.templateCard {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.templateCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #d1d5db;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.templateInfo {
  flex: 1;
}

.templateName {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.templateType {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.templateSubject {
  font-size: 14px;
  color: #374151;
  margin: 0;
  font-style: italic;
}

.templateBadges {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-end;
}

.defaultBadge {
  background: #fbbf24;
  color: #92400e;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.statusBadge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.statusBadge.active {
  background: #d1fae5;
  color: #065f46;
}

.statusBadge.inactive {
  background: #fee2e2;
  color: #991b1b;
}

.templateVariables {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 20px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.cardActions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.cardActions button {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.previewBtn {
  background: #eff6ff;
  color: #1d4ed8;
  border-color: #dbeafe;
}

.previewBtn:hover {
  background: #dbeafe;
  border-color: #93c5fd;
}

.editBtn {
  background: #f0fdf4;
  color: #166534;
  border-color: #dcfce7;
}

.editBtn:hover {
  background: #dcfce7;
  border-color: #bbf7d0;
}

.defaultBtn {
  background: #fffbeb;
  color: #92400e;
  border-color: #fed7aa;
}

.defaultBtn:hover {
  background: #fed7aa;
  border-color: #fdba74;
}

.toggleBtn {
  border: 1px solid;
}

.toggleBtn.activate {
  background: #f0fdf4;
  color: #166534;
  border-color: #dcfce7;
}

.toggleBtn.activate:hover {
  background: #dcfce7;
  border-color: #bbf7d0;
}

.toggleBtn.deactivate {
  background: #fef2f2;
  color: #dc2626;
  border-color: #fecaca;
}

.toggleBtn.deactivate:hover {
  background: #fecaca;
  border-color: #fca5a5;
}

.deleteBtn {
  background: #fef2f2;
  color: #dc2626;
  border-color: #fecaca;
}

.deleteBtn:hover {
  background: #fecaca;
  border-color: #fca5a5;
}

.emptyState {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  background: #f9fafb;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
}

.emptyState h3 {
  font-size: 20px;
  color: #374151;
  margin: 0 0 8px 0;
}

.emptyState p {
  color: #6b7280;
  margin: 0 0 24px 0;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .templatesContainer {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .headerActions {
    justify-content: flex-start;
  }

  .filters {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .templatesGrid {
    grid-template-columns: 1fr;
  }

  .cardActions {
    justify-content: center;
  }

  .cardActions button {
    flex: 1;
    min-width: 0;
  }
}
