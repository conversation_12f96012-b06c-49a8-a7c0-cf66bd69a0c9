import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useAuth } from '../../../hooks/useAuth';
import AdminLayout from '../../../components/admin/AdminLayout';
import styles from '../../../styles/admin/StaffOnboarding.module.css';

interface ChecklistItem {
  id: string;
  checklist_item: string;
  category: string;
  description?: string;
  is_required: boolean;
  is_completed: boolean;
  completed_at?: string;
  due_date?: string;
  notes?: string;
  created_at: string;
}

interface OnboardingStatistics {
  total: number;
  completed: number;
  required: number;
  completedRequired: number;
  completionPercentage: number;
  requiredCompletionPercentage: number;
}

const CATEGORY_LABELS = {
  documentation: 'Documentation',
  training: 'Training',
  equipment: 'Equipment',
  access: 'Access & Setup'
};

const CATEGORY_ICONS = {
  documentation: '📄',
  training: '🎓',
  equipment: '🛠️',
  access: '🔑'
};

export default function StaffOnboardingPage() {
  const { user } = useAuth();
  const router = useRouter();
  const { staff_id } = router.query;
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [checklist, setChecklist] = useState<ChecklistItem[]>([]);
  const [statistics, setStatistics] = useState<OnboardingStatistics>({
    total: 0,
    completed: 0,
    required: 0,
    completedRequired: 0,
    completionPercentage: 0,
    requiredCompletionPercentage: 0
  });
  const [staffInfo, setStaffInfo] = useState<any>(null);
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    if (staff_id) {
      loadOnboardingData();
      loadStaffInfo();
    }
  }, [staff_id]);

  const loadOnboardingData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/staff/onboarding?staff_id=${staff_id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setChecklist(data.checklist || []);
        setStatistics(data.statistics || {});
      } else if (response.status === 404) {
        // No checklist exists, offer to initialize
        setChecklist([]);
      } else {
        setError('Failed to load onboarding data');
      }
    } catch (error) {
      console.error('Error loading onboarding data:', error);
      setError('Failed to load onboarding data');
    } finally {
      setLoading(false);
    }
  };

  const loadStaffInfo = async () => {
    try {
      const response = await fetch(`/api/admin/staff?id=${staff_id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStaffInfo(data.staff);
      }
    } catch (error) {
      console.error('Error loading staff info:', error);
    }
  };

  const initializeOnboarding = async () => {
    try {
      const response = await fetch('/api/admin/staff/onboarding', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        },
        body: JSON.stringify({
          staff_id,
          action: 'initialize'
        })
      });

      if (response.ok) {
        await loadOnboardingData();
      } else {
        setError('Failed to initialize onboarding checklist');
      }
    } catch (error) {
      console.error('Error initializing onboarding:', error);
      setError('Failed to initialize onboarding checklist');
    }
  };

  const toggleItemCompletion = async (itemId: string, isCompleted: boolean) => {
    try {
      const response = await fetch('/api/admin/staff/onboarding', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        },
        body: JSON.stringify({
          staff_id,
          action: isCompleted ? 'uncomplete_item' : 'complete_item',
          checklist_item_id: itemId
        })
      });

      if (response.ok) {
        await loadOnboardingData();
      } else {
        setError('Failed to update checklist item');
      }
    } catch (error) {
      console.error('Error updating checklist item:', error);
      setError('Failed to update checklist item');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return '#10b981';
    if (percentage >= 70) return '#f59e0b';
    return '#ef4444';
  };

  const filteredChecklist = selectedCategory === 'all' 
    ? checklist 
    : checklist.filter(item => item.category === selectedCategory);

  const groupedChecklist = filteredChecklist.reduce((groups, item) => {
    const category = item.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(item);
    return groups;
  }, {} as Record<string, ChecklistItem[]>);

  if (loading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading onboarding data...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Staff Onboarding | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage staff onboarding process and checklist" />
      </Head>

      <div className={styles.onboardingContainer}>
        <header className={styles.header}>
          <div className={styles.headerLeft}>
            <h1 className={styles.title}>Staff Onboarding</h1>
            {staffInfo && (
              <p className={styles.subtitle}>
                {staffInfo.firstName} {staffInfo.lastName} - {staffInfo.role}
              </p>
            )}
          </div>
          <div className={styles.headerActions}>
            <button
              onClick={() => router.back()}
              className={styles.backBtn}
            >
              ← Back to Staff
            </button>
          </div>
        </header>

        {error && (
          <div className={styles.errorMessage}>
            {error}
            <button onClick={() => setError(null)} className={styles.closeError}>×</button>
          </div>
        )}

        {checklist.length === 0 ? (
          <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>📋</div>
            <h3>No Onboarding Checklist</h3>
            <p>This staff member doesn't have an onboarding checklist yet.</p>
            <button
              onClick={initializeOnboarding}
              className={styles.initializeBtn}
            >
              Initialize Onboarding Checklist
            </button>
          </div>
        ) : (
          <>
            <div className={styles.progressSection}>
              <div className={styles.progressCard}>
                <h3>Overall Progress</h3>
                <div className={styles.progressBar}>
                  <div 
                    className={styles.progressFill}
                    style={{ 
                      width: `${statistics.completionPercentage}%`,
                      backgroundColor: getProgressColor(statistics.completionPercentage)
                    }}
                  ></div>
                </div>
                <div className={styles.progressStats}>
                  <span>{statistics.completed} of {statistics.total} completed</span>
                  <span>{statistics.completionPercentage}%</span>
                </div>
              </div>

              <div className={styles.progressCard}>
                <h3>Required Items</h3>
                <div className={styles.progressBar}>
                  <div 
                    className={styles.progressFill}
                    style={{ 
                      width: `${statistics.requiredCompletionPercentage}%`,
                      backgroundColor: getProgressColor(statistics.requiredCompletionPercentage)
                    }}
                  ></div>
                </div>
                <div className={styles.progressStats}>
                  <span>{statistics.completedRequired} of {statistics.required} completed</span>
                  <span>{statistics.requiredCompletionPercentage}%</span>
                </div>
              </div>
            </div>

            <div className={styles.filters}>
              <div className={styles.filterGroup}>
                <label htmlFor="categoryFilter">Filter by Category:</label>
                <select
                  id="categoryFilter"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className={styles.filterSelect}
                >
                  <option value="all">All Categories</option>
                  {Object.entries(CATEGORY_LABELS).map(([key, label]) => (
                    <option key={key} value={key}>{label}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className={styles.checklistContent}>
              {Object.entries(groupedChecklist).map(([category, items]) => (
                <div key={category} className={styles.categorySection}>
                  <h3 className={styles.categoryHeader}>
                    <span className={styles.categoryIcon}>
                      {CATEGORY_ICONS[category as keyof typeof CATEGORY_ICONS]}
                    </span>
                    {CATEGORY_LABELS[category as keyof typeof CATEGORY_LABELS]}
                    <span className={styles.categoryCount}>
                      ({items.filter(item => item.is_completed).length}/{items.length})
                    </span>
                  </h3>

                  <div className={styles.checklistItems}>
                    {items.map((item) => (
                      <div 
                        key={item.id} 
                        className={`${styles.checklistItem} ${item.is_completed ? styles.completed : ''}`}
                      >
                        <div className={styles.itemHeader}>
                          <label className={styles.checkboxLabel}>
                            <input
                              type="checkbox"
                              checked={item.is_completed}
                              onChange={() => toggleItemCompletion(item.id, item.is_completed)}
                              className={styles.checkbox}
                            />
                            <span className={styles.itemTitle}>
                              {item.checklist_item}
                              {item.is_required && (
                                <span className={styles.requiredBadge}>Required</span>
                              )}
                            </span>
                          </label>
                          {item.due_date && (
                            <span className={styles.dueDate}>
                              Due: {formatDate(item.due_date)}
                            </span>
                          )}
                        </div>

                        {item.description && (
                          <p className={styles.itemDescription}>{item.description}</p>
                        )}

                        {item.is_completed && item.completed_at && (
                          <div className={styles.completionInfo}>
                            ✅ Completed on {formatDate(item.completed_at)}
                          </div>
                        )}

                        {item.notes && (
                          <div className={styles.itemNotes}>
                            <strong>Notes:</strong> {item.notes}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </AdminLayout>
  );
}
