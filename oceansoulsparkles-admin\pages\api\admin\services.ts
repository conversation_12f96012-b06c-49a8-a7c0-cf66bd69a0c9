import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminToken } from '../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET' && req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '') || 
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const authResult = await verifyAdminToken(token);
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    const user = authResult.user;

    // Only admin and dev can manage services
    if (user.role !== 'Admin' && user.role !== 'DEV') {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    if (req.method === 'POST') {
      // Create new service
      const {
        name,
        description,
        duration,
        price,
        category,
        status,
        visible_on_public,
        visible_on_pos,
        visible_on_events
      } = req.body;

      // Validate required fields
      if (!name || !category) {
        return res.status(400).json({ error: 'Name and category are required' });
      }

      const { data: service, error } = await supabase
        .from('services')
        .insert([{
          name,
          description,
          duration: duration ? parseInt(duration) : null,
          price: price ? parseFloat(price) : null,
          category,
          status: status || 'active',
          visible_on_public: visible_on_public !== false,
          visible_on_pos: visible_on_pos !== false,
          visible_on_events: visible_on_events !== false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        console.error('Service creation error:', error);
        return res.status(500).json({ error: 'Failed to create service' });
      }

      return res.status(201).json({
        service: {
          ...service,
          is_active: service.status === 'active',
          total_bookings: 0
        }
      });
    }

    // GET services from database
    const { data: services, error } = await supabase
      .from('services')
      .select(`
        id,
        name,
        description,
        duration,
        base_price,
        category,
        is_active,
        created_at,
        updated_at
      `)
      .order('category', { ascending: true })
      .order('name', { ascending: true });

    if (error) {
      console.error('Services query error:', error);
      return res.status(500).json({ error: 'Failed to fetch services' });
    }

    // Get booking counts for each service
    const serviceIds = services?.map(s => s.id) || [];
    const { data: bookingCounts } = await supabase
      .from('bookings')
      .select('service_id')
      .in('service_id', serviceIds);

    // Count bookings per service
    const bookingCountMap = (bookingCounts || []).reduce((acc, booking) => {
      acc[booking.service_id] = (acc[booking.service_id] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Transform data
    const transformedServices = (services || []).map(service => ({
      id: service.id,
      name: service.name,
      description: service.description,
      duration: service.duration,
      base_price: service.base_price,
      price: service.base_price, // For backward compatibility
      category: service.category,
      is_active: service.is_active,
      status: service.is_active ? 'active' : 'inactive',
      total_bookings: bookingCountMap[service.id] || 0,
      created_at: service.created_at,
      updated_at: service.updated_at
    }));

    return res.status(200).json({
      services: transformedServices,
      total: transformedServices.length
    });

  } catch (error) {
    console.error('Services API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
